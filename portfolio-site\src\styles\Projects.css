.projects {
  background-color: #f9f9f9;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2.5rem;
}

.project-card {
  background-color: #fff;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.project-image {
  width: 100%;
  height: 220px;
  overflow: hidden;
  position: relative;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #888;
}

.placeholder-image span {
  font-size: 3rem;
  color: var(--primary-color);
  opacity: 0.7;
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(14, 17, 51, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.view-project-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-md);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.view-project-btn:hover {
  background-color: var(--hover-color);
  transform: translateY(-2px);
}

.project-content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.project-title {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.project-description {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 0.95rem;
  flex-grow: 1;
}

.project-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.technology-tag {
  background-color: rgba(74, 108, 247, 0.1);
  color: var(--primary-color);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.more-tag {
  background-color: rgba(108, 117, 125, 0.1);
  color: var(--secondary-color);
}

.project-links {
  display: flex;
  gap: 1rem;
  margin-top: auto;
}

.project-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Project Modal Styles */
.project-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.project-modal {
  background-color: white;
  border-radius: var(--border-radius-lg);
  width: 100%;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.close-modal {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--secondary-color);
  cursor: pointer;
  z-index: 10;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: white;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.close-modal:hover {
  color: var(--primary-color);
  transform: rotate(90deg);
}

.modal-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.modal-image {
  height: 100%;
  overflow: hidden;
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

.modal-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modal-details {
  padding: 2rem 2rem 2rem 0;
}

.modal-details h2 {
  color: var(--dark-background);
  margin-bottom: 1rem;
}

.modal-description {
  color: var(--secondary-color);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.modal-section {
  margin-bottom: 1.5rem;
}

.modal-section h3 {
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  color: var(--dark-background);
}

.feature-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: var(--text-color);
}

.feature-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

.modal-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.modal-links {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.modal-links .btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Responsive styles */
@media (max-width: 992px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .modal-content {
    grid-template-columns: 1fr;
  }

  .modal-image {
    height: 300px;
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
  }

  .modal-details {
    padding: 0 2rem 2rem 2rem;
  }
}

@media (max-width: 768px) {
  .project-modal {
    max-height: 85vh;
  }
}

@media (max-width: 576px) {
  .projects-grid {
    grid-template-columns: 1fr;
  }

  .project-modal-overlay {
    padding: 1rem;
  }

  .modal-image {
    height: 200px;
  }
}
