import { useState } from 'react';
import { FaGithub, FaExternalLinkAlt, FaSearch, FaTimes } from 'react-icons/fa';
import '../styles/Projects.css';

const Projects = () => {
  const [selectedProject, setSelectedProject] = useState(null);

  const projects = [
    {
      id: 1,
      title: 'Expense Tracker App',
      description: 'A comprehensive expense tracking application that helps users manage their finances, track income and expenses, and visualize spending patterns.',
      longDescription: 'This expense tracker application provides a complete solution for personal finance management. Built with React for the frontend and Node.js with Express for the backend, it offers a responsive and intuitive interface for tracking daily transactions. The app includes features like transaction categorization, monthly budget planning, and detailed financial reports with interactive charts for better visualization of spending patterns.',
      technologies: ['React', 'Node.js', 'Express', 'MongoDB', 'Chart.js', 'CSS3'],
      features: [
        'Income and expense tracking with categorization',
        'Monthly budget planning and monitoring',
        'Interactive charts and reports for spending analysis',
        'Transaction history with filtering options',
        'User authentication and profile management',
        'Responsive design for mobile and desktop use'
      ],
      image: '/images/projects/expense-tracker.svg',
      githubLink: 'https://github.com/rahulhaque/expense-tracker-react',
      liveLink: 'https://expense-tracker-demo.netlify.app',
    },
    {
      id: 2,
      title: 'Task Management System',
      description: 'A collaborative task management application with real-time updates, task assignment, and progress tracking features.',
      longDescription: 'This task management system helps teams organize and track their work efficiently. It provides a visual board interface similar to Trello, with the ability to create multiple boards, lists, and cards. Users can assign tasks, set due dates, add labels, and track progress in real-time.',
      technologies: ['React', 'Firebase', 'Redux', 'Material UI', 'Socket.io'],
      features: [
        'Drag-and-drop interface for task management',
        'Real-time collaboration and updates',
        'Task assignment and due date tracking',
        'File attachments and comments',
        'Email notifications for task updates',
        'Mobile-responsive design'
      ],
      image: '/images/projects/placeholder.svg',
      githubLink: 'https://github.com/yourusername/task-management-app',
      liveLink: 'https://task-management-demo.netlify.app',
    },
    {
      id: 3,
      title: 'Weather Dashboard',
      description: 'An interactive weather application that provides current conditions, forecasts, and historical weather data for locations worldwide.',
      longDescription: 'This weather dashboard integrates with multiple weather APIs to provide comprehensive weather information. Users can search for any location and view current conditions, hourly forecasts, 7-day forecasts, and historical weather data. The app also includes interactive maps, severe weather alerts, and customizable units.',
      technologies: ['JavaScript', 'React', 'Chart.js', 'OpenWeatherMap API', 'Mapbox API'],
      features: [
        'Location-based weather forecasts',
        'Interactive weather maps',
        'Historical weather data visualization',
        'Severe weather alerts',
        'Customizable units (metric/imperial)',
        'Responsive design for all devices'
      ],
      image: '/images/projects/placeholder.svg',
      githubLink: 'https://github.com/yourusername/weather-dashboard',
      liveLink: 'https://weather-dashboard-demo.netlify.app',
    },
    {
      id: 4,
      title: 'Portfolio Website',
      description: 'A modern, responsive portfolio website built with React and Vite to showcase my projects and skills.',
      longDescription: 'This portfolio website serves as a digital resume and project showcase. Built with React and Vite for optimal performance, it features a clean, modern design with smooth animations and transitions. The site is fully responsive and includes sections for projects, skills, about me, and contact information.',
      technologies: ['React', 'Vite', 'CSS Modules', 'React Icons', 'Framer Motion'],
      features: [
        'Modern, responsive design',
        'Project showcase with detailed information',
        'Skills visualization',
        'Contact form with validation',
        'Performance optimized with Vite',
        'Smooth animations and transitions'
      ],
      image: '/images/projects/placeholder.svg',
      githubLink: 'https://github.com/yourusername/portfolio',
      liveLink: 'https://yourportfolio.com',
    },
    {
      id: 5,
      title: 'Fitness Tracking App',
      description: 'A comprehensive fitness application for tracking workouts, nutrition, and progress with personalized recommendations.',
      longDescription: 'This fitness tracking application helps users maintain a healthy lifestyle by tracking workouts, nutrition, and overall progress. Users can create custom workout routines, log exercises and meals, set goals, and view detailed progress reports. The app also provides personalized recommendations based on user data and goals.',
      technologies: ['React Native', 'Firebase', 'Redux', 'Chart.js', 'Nutritionix API'],
      features: [
        'Workout planning and tracking',
        'Nutrition logging and calorie counting',
        'Progress visualization with charts',
        'Goal setting and achievement tracking',
        'Personalized recommendations',
        'Social sharing and community features'
      ],
      image: '/images/projects/placeholder.svg',
      githubLink: 'https://github.com/yourusername/fitness-tracker',
      liveLink: 'https://fitness-tracker-demo.netlify.app',
    },
    {
      id: 6,
      title: 'Real Estate Marketplace',
      description: 'A platform for buying, selling, and renting properties with advanced search, virtual tours, and mortgage calculator.',
      longDescription: 'This real estate marketplace connects property buyers, sellers, and renters. It features advanced property search with filters, virtual property tours, mortgage calculator, and direct messaging between users. Property listings include detailed information, high-quality images, floor plans, and neighborhood data.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Express', 'Google Maps API', 'Cloudinary'],
      features: [
        'Advanced property search and filtering',
        'Virtual property tours',
        'Mortgage calculator and affordability tools',
        'Property listing management',
        'User messaging system',
        'Neighborhood information and analytics'
      ],
      image: '/images/projects/placeholder.svg',
      githubLink: 'https://github.com/yourusername/real-estate-marketplace',
      liveLink: 'https://real-estate-demo.netlify.app',
    },
  ];

  const openProjectModal = (project) => {
    setSelectedProject(project);
    document.body.style.overflow = 'hidden';
  };

  const closeProjectModal = () => {
    setSelectedProject(null);
    document.body.style.overflow = 'auto';
  };

  return (
    <section id="projects" className="projects">
      <div className="container">
        <h2 className="section-title">My Projects</h2>
        <p className="section-description">
          Here are some of the projects I've worked on. Click on any project to learn more.
        </p>
        <div className="projects-grid">
          {projects.map((project) => (
            <div className="project-card" key={project.id}>
              <div className="project-image">
                {project.image ? (
                  <img src={project.image} alt={project.title} />
                ) : (
                  <div className="placeholder-image">
                    <span>{project.title[0]}</span>
                  </div>
                )}
                <div className="project-overlay">
                  <button
                    className="view-project-btn"
                    onClick={() => openProjectModal(project)}
                  >
                    <FaSearch /> View Details
                  </button>
                </div>
              </div>
              <div className="project-content">
                <h3 className="project-title">{project.title}</h3>
                <p className="project-description">{project.description}</p>
                <div className="project-technologies">
                  {project.technologies.slice(0, 4).map((tech, techIndex) => (
                    <span className="technology-tag" key={techIndex}>
                      {tech}
                    </span>
                  ))}
                  {project.technologies.length > 4 && (
                    <span className="technology-tag more-tag">
                      +{project.technologies.length - 4}
                    </span>
                  )}
                </div>
                <div className="project-links">
                  <a
                    href={project.githubLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="project-link"
                  >
                    <FaGithub /> Code
                  </a>
                  <a
                    href={project.liveLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="project-link"
                  >
                    <FaExternalLinkAlt /> Live Demo
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Project Modal */}
        {selectedProject && (
          <div className="project-modal-overlay" onClick={closeProjectModal}>
            <div className="project-modal" onClick={(e) => e.stopPropagation()}>
              <button className="close-modal" onClick={closeProjectModal}>
                <FaTimes />
              </button>
              <div className="modal-content">
                <div className="modal-image">
                  {selectedProject.image ? (
                    <img src={selectedProject.image} alt={selectedProject.title} />
                  ) : (
                    <div className="placeholder-image">
                      <span>{selectedProject.title[0]}</span>
                    </div>
                  )}
                </div>
                <div className="modal-details">
                  <h2>{selectedProject.title}</h2>
                  <p className="modal-description">{selectedProject.longDescription}</p>

                  <div className="modal-section">
                    <h3>Key Features</h3>
                    <ul className="feature-list">
                      {selectedProject.features.map((feature, index) => (
                        <li key={index}>{feature}</li>
                      ))}
                    </ul>
                  </div>

                  <div className="modal-section">
                    <h3>Technologies Used</h3>
                    <div className="modal-technologies">
                      {selectedProject.technologies.map((tech, index) => (
                        <span className="technology-tag" key={index}>{tech}</span>
                      ))}
                    </div>
                  </div>

                  <div className="modal-links">
                    <a
                      href={selectedProject.githubLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn btn-primary"
                    >
                      <FaGithub /> View Code
                    </a>
                    <a
                      href={selectedProject.liveLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn btn-secondary"
                    >
                      <FaExternalLinkAlt /> Live Demo
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default Projects;
